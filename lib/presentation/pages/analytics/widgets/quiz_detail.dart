import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scrumpass_exam_simulator/app/config/app_colors.dart';
import 'package:scrumpass_exam_simulator/app/config/app_text_styles.dart';
import 'package:scrumpass_exam_simulator/app/extensions/double.dart';
import 'package:scrumpass_exam_simulator/generated/assets.gen.dart';
import 'package:scrumpass_exam_simulator/generated/locales.g.dart';
import 'package:scrumpass_exam_simulator/presentation/controllers/analytics/analytics_controller.dart';
import 'package:scrumpass_exam_simulator/presentation/pages/analytics/widgets/statistic_card.dart';

class QuizDetail extends GetView<AnalyticsController> {
  const QuizDetail({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return StatisticCard(
      enableBlur: true,
      icon: Assets.images.statPin.path,
      title: LocaleKeys.quizDetailTitle.tr,
      bgColor: AppColors.analyticsQuizDetailGradient,
      child: Padding(
        padding: const EdgeInsets.only(left: 4, top: 20, right: 10),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          widget(
                            child: Text(
                              "${controller.averageScore.roundedPrecisionToString(0)}%",
                              style: AppTextStyles.baseBold.copyWith(
                                  color: AppColors.text1st, fontSize: 36),
                            ),
                          ),
                          const SizedBox(width: 2),
                          if (controller.quizDetailAverageChange ==
                              QuizDetailAverageChange.increase)
                            Assets.images.arrowRightUp.svg(),
                          if (controller.quizDetailAverageChange ==
                              QuizDetailAverageChange.decrease)
                            Assets.images.arrowRightDown.svg(),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(LocaleKeys.average.tr,
                          style: AppTextStyles.baseMedium
                              .copyWith(color: AppColors.textHint)),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      controller.results.length.toString(),
                      style: AppTextStyles.xLargeSemiBold
                          .copyWith(color: AppColors.text1st),
                    ),
                    const SizedBox(height: 4),
                    Text(LocaleKeys.testDone.tr,
                        style: AppTextStyles.smallMedium
                            .copyWith(color: AppColors.textHint)),
                  ],
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${controller.latestScore.roundedPrecisionToString(0)}%",
                      style: AppTextStyles.xLargeSemiBold
                          .copyWith(color: AppColors.text1st),
                    ),
                    const SizedBox(height: 4),
                    Text(LocaleKeys.latestScore.tr,
                        style: AppTextStyles.smallMedium
                            .copyWith(color: AppColors.textHint)),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: Text(
                LocaleKeys.avgScore.tr,
                style: AppTextStyles.xxSmallRegular
                    .copyWith(color: AppColors.textHint),
                textAlign: TextAlign.left,
              ),
            ),
            const SizedBox(height: 15),
            SizedBox(
                width: double.infinity,
                height: 180,
                child: LineChart(mainData())),
            Text(
              LocaleKeys.testDone.tr,
              style: AppTextStyles.xxSmallRegular
                  .copyWith(color: AppColors.textHint),
            )
          ],
        ),
      ),
    );
  }

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    final style =
        AppTextStyles.xxSmallMedium.copyWith(color: AppColors.textHint);

    final text = Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Text((value == 0 ? 1 : value).toStringAsFixed(0), style: style),
    );

    return meta.max < 20
        ? SideTitleWidget(
            axisSide: meta.axisSide,
            child: text,
          )
        : (value) % 10 == 0
            ? text
            : const SizedBox();
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    final style =
        AppTextStyles.xxSmallRegular.copyWith(color: AppColors.textHint);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(value.toStringAsFixed(0),
          style: style, textAlign: TextAlign.left),
    );
  }

  LineChartData mainData() {
    final data = controller.score.reversed.toList();
    final range = getRange(data.length);
    return LineChartData(
      lineTouchData: LineTouchData(enabled: false),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        drawHorizontalLine: true,
        horizontalInterval: 20,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: AppColors.green1,
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: bottomTitleWidgets,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 20,
            getTitlesWidget: leftTitleWidgets,
            reservedSize: 20,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border(
          top: BorderSide(color: AppColors.green1),
          bottom: BorderSide(color: AppColors.green1),
        ),
      ),
      minX: range['start']?.toDouble(),
      maxX: range['end']?.toDouble(),
      minY: 0,
      maxY: 100,
      lineBarsData: [
        LineChartBarData(
          spots: [
            if (data.isEmpty) FlSpot(0, 0),
            for (var i = 0; i < data.length; i++) ...{
              FlSpot(i.toDouble(), data[i]),
            }
          ],
          isCurved: true,
          preventCurveOverShooting: true,
          preventCurveOvershootingThreshold: 0,
          color: AppColors.green5,
          barWidth: 1.5,
          isStrokeCapRound: true,
          dotData: const FlDotData(
            show: false,
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: AppColors.quizDetailChartGradient,
            ),
          ),
        ),
      ],
    );
  }

  Map<String, int> getRange(int x) {
    int start = 0;
    int end = 0;

    if (x >= 0 && x <= 60) {
      start = 0;
      end = 60;
    } else if (x > 60 && x < 80) {
      start = 20;
      end = 80;
    } else if (x >= 80 && x < 100) {
      start = 40;
      end = 100;
    } else if (x >= 100 && x < 120) {
      start = 60;
      end = 120;
    } else if (x >= 120 && x < 140) {
      start = 80;
      end = 140;
    } else if (x >= 140 && x < 160) {
      start = 100;
      end = 160;
    } else if (x < 0) {
      // Nếu x nhỏ hơn 0, thu nhỏ khoảng theo quy luật ngược lại
      int diff = (-x ~/ 20); // Lấy phần nguyên khi chia 20 để xác định khoảng
      start = -diff * 20 - 20;
      end = -diff * 20 + 40;
    } else {
      // Tăng khoảng theo quy luật 20 đơn vị cho giá trị x lớn hơn
      int diff = (x - 100) ~/ 20; // Lấy phần nguyên để xác định khoảng
      start = 60 + diff * 20;
      end = 120 + diff * 20;
    }

    return {'start': start, 'end': end};
  }
}
