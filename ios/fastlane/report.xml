<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="00: default_platform" time="0.000137">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="01: Switch to ios build lane" time="7.3e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="02: Switch to ios sh_on_root lane" time="4.1e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="03: cd /Users/<USER>/scrumpass-exam-simulator &amp;&amp; flutter build ipa" time="140.86513">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="04: Switch to ios archive lane" time="0.000993">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="05: update_code_signing_settings" time="0.039908">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="06: Switch to ios increment_version lane" time="5.5e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="07: firebase_app_distribution_get_latest_release" time="0.660429">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="08: increment_build_number" time="0.153499">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="09: update_project_team" time="0.004816">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="10: build_app" time="123.646121">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="11: firebase_app_distribution" time="9.715355">
        
      </testcase>
    
  </testsuite>
</testsuites>
